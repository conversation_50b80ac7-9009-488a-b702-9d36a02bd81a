import React, { useRef } from 'react';
import { View, Text, TouchableOpacity, Image, FlatList } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation, useRouter } from 'expo-router';
import { Ionico<PERSON>, Feather } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import { useColorScheme } from '~/lib/useColorScheme';
import { Story, StoryViewer } from '~/types/story_type';
import ViewersBottomSheet, {
  ViewersBottomSheetHandle,
} from '~/components/Story/ViewersBottomSheet';

// Mock data for stories - combined active and expired
const mockStories: Story[] = [
  {
    id: 1,
    userId: 1,
    userName: 'You',
    userProfilePhoto: 'https://images.unsplash.com/photo-1507152832244-10d45c7eda57',
    mediaUrl: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f',
    mediaType: 'image',
    caption: 'Having a great time!',
    createdAt: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
    expiresAt: new Date(Date.now() + 82800000).toISOString(), // 23 hours from now
    viewers: [
      {
        id: 2,
        name: 'John Doe',
        profilePhoto: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36',
        viewedAt: new Date(Date.now() - 1800000).toISOString(), // 30 minutes ago
      },
      {
        id: 3,
        name: 'Jane Smith',
        profilePhoto: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330',
        viewedAt: new Date(Date.now() - 900000).toISOString(), // 15 minutes ago
      },
    ],
    textColor: '#FFFFFF',
  },
  {
    id: 2,
    userId: 1,
    userName: 'You',
    userProfilePhoto: 'https://images.unsplash.com/photo-1507152832244-10d45c7eda57',
    mediaUrl: 'https://images.unsplash.com/photo-1501281668745-f7f57925c3b4',
    mediaType: 'image',
    caption: 'Weekend vibes',
    createdAt: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    expiresAt: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
    viewers: [
      {
        id: 2,
        name: 'John Doe',
        profilePhoto: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36',
        viewedAt: new Date(Date.now() - 90000000).toISOString(),
      },
      {
        id: 3,
        name: 'Jane Smith',
        profilePhoto: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330',
        viewedAt: new Date(Date.now() - 88000000).toISOString(),
      },
      {
        id: 4,
        name: 'Mike Johnson',
        profilePhoto: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d',
        viewedAt: new Date(Date.now() - 86500000).toISOString(),
      },
    ],
    textColor: '#FFFFFF',
  },
  {
    id: 3,
    userId: 1,
    userName: 'You',
    userProfilePhoto: 'https://images.unsplash.com/photo-1507152832244-10d45c7eda57',
    mediaUrl: 'https://images.unsplash.com/photo-1534438327276-14e5300c3a48',
    mediaType: 'image',
    caption: 'Morning coffee ☕',
    createdAt: new Date(Date.now() - 259200000).toISOString(), // 3 days ago
    expiresAt: new Date(Date.now() - 172800000).toISOString(), // 2 days ago
    viewers: [
      {
        id: 2,
        name: 'John Doe',
        profilePhoto: 'https://images.unsplash.com/photo-1599566150163-29194dcaad36',
        viewedAt: new Date(Date.now() - 250000000).toISOString(),
      },
      {
        id: 5,
        name: 'Sarah Williams',
        profilePhoto: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80',
        viewedAt: new Date(Date.now() - 245000000).toISOString(),
      },
    ],
    textColor: '#FFFFFF',
  },
];

export default function MyStoriesScreen() {
  const navigation = useNavigation();
  const router = useRouter();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';
  const insets = useSafeAreaInsets();
  const viewersBottomSheetRef = useRef<ViewersBottomSheetHandle>(null);

  const formatTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

    if (diffInSeconds < 60) {
      return `${diffInSeconds}s ago`;
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m ago`;
    } else if (diffInSeconds < 86400) {
      return `${Math.floor(diffInSeconds / 3600)}h ago`;
    } else {
      return `${Math.floor(diffInSeconds / 86400)}d ago`;
    }
  };

  const formatTimeLeft = (expiresAt: string) => {
    const expiryDate = new Date(expiresAt);
    const now = new Date();
    const diffInSeconds = Math.floor((expiryDate.getTime() - now.getTime()) / 1000);

    if (diffInSeconds < 0) {
      return 'Expired';
    } else if (diffInSeconds < 3600) {
      return `${Math.floor(diffInSeconds / 60)}m left`;
    } else {
      return `${Math.floor(diffInSeconds / 3600)}h left`;
    }
  };

  const handleCreateStory = () => {
    router.push('/story/create');
  };

  const handleViewStory = (story: Story) => {
    // Navigate to view story screen with the story data
    router.push({
      pathname: '/story/view',
      params: { storyId: story.id },
    });
  };

  const handleViewViewers = (viewers: StoryViewer[]) => {
    viewersBottomSheetRef.current?.present(viewers);
  };

  const isStoryActive = (story: Story) => {
    const expiryDate = new Date(story.expiresAt);
    const now = new Date();
    return expiryDate > now;
  };

  const renderStoryItem = ({ item }: { item: Story }) => {
    const isActive = isStoryActive(item);

    return (
      <View
        className={`mb-4 overflow-hidden rounded-xl ${isActive ? 'border-violet-600' : 'border-gray-300'}`}
        style={{
          borderWidth: 1,
          backgroundColor: isDark ? colors.grey6 : colors.grey5,
          shadowColor: isDark ? '#000' : '#888',
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
          elevation: 2,
        }}>
        <TouchableOpacity
          className="flex-row items-center p-4"
          onPress={() => handleViewStory(item)}>
          <View className="relative mr-3">
            {isActive ? (
              <View className="rounded-full bg-violet-600 p-0.5">
                <Image
                  source={{ uri: item.mediaUrl }}
                  className="h-16 w-16 rounded-full"
                  resizeMode="cover"
                />
              </View>
            ) : (
              <Image
                source={{ uri: item.mediaUrl }}
                className="h-16 w-16 rounded-full"
                resizeMode="cover"
              />
            )}
          </View>
          <View className="flex-1">
            <Text className={`font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
              {item.caption || 'My story'}
            </Text>
            <View className="mt-1 flex-row items-center">
              <TouchableOpacity
                className="flex-row items-center rounded-full bg-gray-100 px-2 py-1 dark:bg-gray-800"
                onPress={() => handleViewViewers(item.viewers)}>
                <Ionicons name="eye-outline" size={14} color={isDark ? '#bbb' : '#777'} />
                <Text className={`ml-1 text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                  {item.viewers.length} {item.viewers.length === 1 ? 'view' : 'views'}
                </Text>
              </TouchableOpacity>
              <Text className={`ml-3 text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
                {isActive ? formatTimeLeft(item.expiresAt) : formatTimeAgo(item.createdAt)}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      </View>
    );
  };

  return (
    <View className="flex-1" style={{ backgroundColor: colors.background }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View
        className="flex-row items-center justify-between px-4 pb-4"
        style={{ paddingTop: insets.top + 10 }}>
        <View className="flex-row items-center">
          <TouchableOpacity className="p-2" onPress={() => navigation.goBack()}>
            <Ionicons name="arrow-back" size={24} color={isDark ? '#fff' : '#000'} />
          </TouchableOpacity>
          <Text className={`ml-2 font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
            My stories
          </Text>
        </View>
        <TouchableOpacity className="rounded-full bg-violet-600 p-2" onPress={handleCreateStory}>
          <Ionicons name="add" size={20} color="#fff" />
        </TouchableOpacity>
      </View>

      <View className="mb-4 px-4">
        <Text className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
          Your status updates are visible for 24 hours
        </Text>
      </View>

      {mockStories.length > 0 ? (
        <FlatList
          data={mockStories}
          renderItem={renderStoryItem}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={{ paddingHorizontal: 16, paddingBottom: 20 }}
          showsVerticalScrollIndicator={false}
        />
      ) : (
        <View className="flex-1 items-center justify-center py-20">
          <Ionicons name="images-outline" size={60} color={isDark ? colors.grey3 : colors.grey4} />
          <Text
            className={`mt-4 text-center font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
            No Stories
          </Text>
          <Text className={`mt-2 text-center ${isDark ? 'text-gray-400' : 'text-gray-500'}`}>
            Your stories will appear here
          </Text>
          <TouchableOpacity
            className="mt-6 rounded-full bg-violet-600 px-6 py-3"
            onPress={handleCreateStory}>
            <Text className="font-medium text-white">Create Story</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Viewers Bottom Sheet */}
      <ViewersBottomSheet ref={viewersBottomSheetRef} />
    </View>
  );
}
