import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  TextInput,
  ScrollView,
  Platform,
  Dimensions,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useNavigation } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import { Ionicons, MaterialIcons, MaterialCommunityIcons } from '@expo/vector-icons';
import { useColorScheme } from '~/lib/useColorScheme';

const { width: SCREEN_WIDTH } = Dimensions.get('window');

export default function CreateStoryScreen() {
  const navigation = useNavigation();
  const { colors, colorScheme } = useColorScheme();
  const isDark = colorScheme === 'dark';

  const [selectedMedia, setSelectedMedia] = useState(null);
  const [caption, setCaption] = useState('');
  const [textColor, setTextColor] = useState('#FFFFFF');
  const [loading, setLoading] = useState(false);

  // Color options for text
  const colorOptions = [
    '#FFFFFF',
    '#000000',
    '#F44336',
    '#2196F3',
    '#FFEB3B',
    '#4CAF50',
    '#FF9800',
  ];

  const pickImage = async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== 'granted') {
        alert('Sorry, we need camera roll permissions to make this work!');
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsEditing: true,
        aspect: [9, 16],
        quality: 1,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedMedia(result.assets[0]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      alert('Failed to pick image');
    }
  };

  const openCamera = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== 'granted') {
        alert('Sorry, we need camera permissions to make this work!');
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.All,
        allowsEditing: true,
        aspect: [9, 16],
        quality: 1,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        setSelectedMedia(result.assets[0]);
      }
    } catch (error) {
      console.error('Error using camera:', error);
      alert('Failed to take photo');
    }
  };

  const handlePost = () => {
    if (!selectedMedia) {
      alert('Please select an image or video for your story');
      return;
    }

    setLoading(true);

    // Mock post request
    setTimeout(() => {
      setLoading(false);
      navigation.goBack();
    }, 1500);
  };

  return (
    <View className={`flex-1 `} style={{ backgroundColor: colors.background }}>
      <StatusBar style={isDark ? 'light' : 'dark'} />

      <View className="flex-row items-center justify-between px-4 pb-4 pt-12">
        <TouchableOpacity className="p-2" onPress={() => navigation.goBack()}>
          <Ionicons name="close" size={28} color={isDark ? '#fff' : '#000'} />
        </TouchableOpacity>
        <Text className={`font-medium text-lg ${isDark ? 'text-white' : 'text-black'}`}>
          Create Story
        </Text>
        <TouchableOpacity
          className={`rounded-full bg-violet-600 px-4 py-2 ${!selectedMedia || loading ? 'opacity-60' : ''}`}
          onPress={handlePost}
          disabled={!selectedMedia || loading}>
          <Text className="font-medium text-white">{loading ? 'Posting...' : 'Post'}</Text>
        </TouchableOpacity>
      </View>

      <ScrollView
        className="flex-1"
        contentContainerStyle={{ flexGrow: 1, padding: 16 }}
        keyboardShouldPersistTaps="handled">
        {selectedMedia ? (
          <View
            className="relative flex-1 overflow-hidden rounded-xl"
            style={{ height: SCREEN_WIDTH * 1.8 }}>
            <Image
              source={{ uri: selectedMedia.uri }}
              className="h-full w-full"
              resizeMode="cover"
            />

            <View className="absolute bottom-20 left-0 right-0 px-4">
              <TextInput
                style={{
                  color: textColor,
                  textShadowColor: 'rgba(0,0,0,0.8)',
                  textShadowOffset: { width: 1, height: 1 },
                  textShadowRadius: 3,
                  fontSize: 18,
                  fontWeight: '600',
                }}
                placeholder="Add a caption..."
                placeholderTextColor="rgba(255,255,255,0.8)"
                value={caption}
                onChangeText={setCaption}
                multiline
                maxLength={100}
              />
            </View>

            <View className="absolute bottom-4 left-0 right-0">
              <ScrollView horizontal showsHorizontalScrollIndicator={false} className="px-4">
                {colorOptions.map((color) => (
                  <TouchableOpacity
                    key={color}
                    className={`mr-3 h-[30px] w-[30px] rounded-full border border-white/50 ${textColor === color ? 'border-[3px] border-violet-600' : ''}`}
                    style={{ backgroundColor: color }}
                    onPress={() => setTextColor(color)}
                  />
                ))}
              </ScrollView>
            </View>

            <TouchableOpacity
              className="absolute right-4 top-4 h-10 w-10 items-center justify-center rounded-full bg-black/60"
              onPress={pickImage}>
              <MaterialIcons name="edit" size={20} color="#fff" />
            </TouchableOpacity>
          </View>
        ) : (
          <View className="flex-1 items-center justify-center px-6">
            <Text
              className={`mb-4 text-center font-bold text-2xl ${isDark ? 'text-white' : 'text-black'}`}>
              Create New Story
            </Text>
            <Text
              className={`mb-10 text-center text-base ${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
              Share a photo or video that will disappear after 24 hours
            </Text>

            <View className="w-full flex-row justify-around">
              <TouchableOpacity
                className={`h-[120px] w-[120px] items-center justify-center rounded-xl `}
                style={{ backgroundColor: colors.grey5 }}
                onPress={pickImage}>
                <MaterialCommunityIcons name="image-multiple" size={32} color="#5A4FCF" />
                <Text
                  className={`mt-3 font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
                  Gallery
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                className={`h-[120px] w-[120px] items-center justify-center rounded-xl `}
                style={{ backgroundColor: colors.grey5 }}
                onPress={openCamera}>
                <Ionicons name="camera" size={32} color="#5A4FCF" />
                <Text
                  className={`mt-3 font-medium text-base ${isDark ? 'text-white' : 'text-black'}`}>
                  Camera
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        )}
      </ScrollView>
    </View>
  );
}
